<template>
  <ele-page class="pdf-demo-page" hide-footer flex-table>
    <ele-card flex-table header="PDF批注组件测试">
      <PdfPostil
        v-model="pdfFile"
        @annotationsChange="handleAnnotationsChange"
      />
    </ele-card>
  </ele-page>
</template>

<script setup>
  import { ref } from 'vue';
  import { ElMessage } from 'element-plus';
  import PdfPostil from '@/components/PdfPostil/index.vue';

  // 响应式数据
  const pdfFile = ref(null);

  // 处理批注变化
  const handleAnnotationsChange = (annotations) => {
    console.log('批注数据更新:', annotations);
    ElMessage.info(`当前共有 ${annotations.length} 个批注`);
  };
</script>

<style lang="scss" scoped>
  .pdf-demo-page {
    height: 100vh;

    :deep(.ele-card) {
      height: calc(100vh - 40px);

      .ele-card__body {
        height: calc(100% - 60px);
        padding: 0;
      }
    }
  }
</style>
