worker_processes  auto;
worker_rlimit_nofile 65535;

events {
    use epoll;
    worker_connections  65535;
}

http{
    include       mime.types;
    default_type  application/octet-stream;
    charset utf-8;

    log_format main '{"@timestamp":"$time_iso8601",'
        '"logType":"nginx_accesslog",'
        '"host":"$server_addr",'
        '"clientip" : "$remote_addr",'
        '"size" : $body_bytes_sent,'
        '"response_time": $request_time,'
        '"upstream_host":"$upstream_addr",'
        '"httphost":"$host",'
        '"referer":"$http_referer",'
        '"xff":"$http_x_forwarded_for",'
        '"agent":"$http_user_agent",'
        '"clientip":"$remote_addr",'
        '"request":"$request",'
        '"uri":"$uri",'
        '"request_method":"$request_method",'
        '"query_string":"$query_string",'
        '"status": $status}';
    access_log /tmp/log/access_log.json main;

    client_max_body_size 300m;
    client_header_buffer_size 32k;
    large_client_header_buffers 4 32k;
    server_names_hash_bucket_size 128;
    sendfile on;
    tcp_nopush     on;
    tcp_nodelay on;
    keepalive_timeout 60;
    client_header_timeout   10;
    client_body_timeout     10;
    send_timeout    10;
    client_body_buffer_size  512k;

    proxy_connect_timeout    5;
    proxy_read_timeout       60;
    proxy_send_timeout       5;
    proxy_buffer_size        16k;
    proxy_buffers            4 64k;
    proxy_busy_buffers_size 128k;
    proxy_temp_file_write_size 128k;

    include conf.d/*.conf;
}
