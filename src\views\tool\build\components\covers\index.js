import CoverInput from './cover-input.vue';
import CoverTextarea from './cover-textarea.vue';
import CoverSelect from './cover-select.vue';
import CoverMultipleSelect from './cover-multiple-select.vue';
import CoverRadio from './cover-radio.vue';
import CoverRadioButton from './cover-radio-button.vue';
import CoverCheckbox from './cover-checkbox.vue';
import CoverCheckboxButton from './cover-checkbox-button.vue';
import CoverDate from './cover-date.vue';
import CoverDatetime from './cover-datetime.vue';
import CoverDaterange from './cover-daterange.vue';
import CoverDatetimerange from './cover-datetimerange.vue';
import CoverTime from './cover-time.vue';
import CoverTimerange from './cover-timerange.vue';
import CoverTimeSelect from './cover-time-select.vue';
import CoverSwitch from './cover-switch.vue';
import CoverInputNumber from './cover-input-number.vue';
import CoverAutocomplete from './cover-autocomplete.vue';
import CoverCascader from './cover-cascader.vue';
import CoverRate from './cover-rate.vue';
import CoverSlider from './cover-slider.vue';
import CoverSliderRange from './cover-slider-range.vue';
import CoverTreeSelect from './cover-tree-select.vue';
import CoverTreeMultipleSelect from './cover-tree-multiple-select.vue';
import CoverVirtualTreeSelect from './cover-virtual-tree-select.vue';
import CoverVirtualTreeMultipleSelect from './cover-virtual-tree-multiple-select.vue';
import CoverTableSelect from './cover-table-select.vue';
import CoverTableMultipleSelect from './cover-table-multiple-select.vue';
import CoverCheckCard from './cover-check-card.vue';
import CoverMultipleCheckCard from './cover-multiple-check-card.vue';
import CoverEditTag from './cover-edit-tag.vue';
import CoverDictRadio from './cover-dict-radio.vue';
import CoverDictSelect from './cover-dict-select.vue';
import CoverDictCheckbox from './cover-dict-checkbox.vue';
import CoverDictMultipleSelect from './cover-dict-multiple-select.vue';
import CoverImageUpload from './cover-image-upload.vue';
import CoverFileUpload from './cover-file-upload.vue';
import CoverRegions from './cover-regions.vue';
import CoverEditor from './cover-editor.vue';
import CoverText from './cover-text.vue';
import CoverLabel from './cover-label.vue';
import CoverDivider from './cover-divider.vue';
import CoverButton from './cover-button.vue';
import CoverSteps from './cover-steps.vue';
import CoverCard from './cover-card.vue';
import CoverDiv from './cover-div.vue';

export const covers = {
  input: CoverInput,
  textarea: CoverTextarea,
  select: CoverSelect,
  multipleSelect: CoverMultipleSelect,
  radio: CoverRadio,
  radioButton: CoverRadioButton,
  checkbox: CoverCheckbox,
  checkboxButton: CoverCheckboxButton,
  date: CoverDate,
  datetime: CoverDatetime,
  daterange: CoverDaterange,
  datetimerange: CoverDatetimerange,
  time: CoverTime,
  timerange: CoverTimerange,
  timeSelect: CoverTimeSelect,
  switch: CoverSwitch,
  inputNumber: CoverInputNumber,
  autocomplete: CoverAutocomplete,
  cascader: CoverCascader,
  rate: CoverRate,
  slider: CoverSlider,
  sliderRange: CoverSliderRange,
  treeSelect: CoverTreeSelect,
  treeMultipleSelect: CoverTreeMultipleSelect,
  virtualTreeSelect: CoverVirtualTreeSelect,
  virtualTreeMultipleSelect: CoverVirtualTreeMultipleSelect,
  tableSelect: CoverTableSelect,
  tableMultipleSelect: CoverTableMultipleSelect,
  checkCard: CoverCheckCard,
  multipleCheckCard: CoverMultipleCheckCard,
  editTag: CoverEditTag,
  dictRadio: CoverDictRadio,
  dictCheckbox: CoverDictCheckbox,
  dictSelect: CoverDictSelect,
  dictMultipleSelect: CoverDictMultipleSelect,
  imageUpload: CoverImageUpload,
  fileUpload: CoverFileUpload,
  regions: CoverRegions,
  editor: CoverEditor,
  text: CoverText,
  label: CoverLabel,
  divider: CoverDivider,
  button: CoverButton,
  steps: CoverSteps,
  card: CoverCard,
  div: CoverDiv
};
