<template>
  <div class="pdf-postil-container">
    <!-- 工具栏 -->
    <div class="toolbar">
      <el-input
        v-model="annotationText"
        placeholder="输入批注内容"
        style="width: 200px; margin-left: 10px"
      />

      <el-select
        v-model="annotationType"
        placeholder="处理方"
        style="width: 120px; margin-left: 10px"
      >
        <el-option label="参与者" value="participator" />
        <el-option label="研究者" value="investigator" />
      </el-select>

      <el-button
        type="success"
        :icon="Plus"
        :disabled="!pdfDoc"
        @click="startAddAnnotation"
      >
        添加批注
      </el-button>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 空状态 -->
      <div v-show="!pdfDoc" class="empty-state">
        <el-empty description="请上传PDF文件开始批注">
          <el-upload
            ref="emptyUploadRef"
            :auto-upload="false"
            :show-file-list="false"
            accept=".pdf"
            @change="handleFileChange"
          >
            <el-button type="primary" size="large" :icon="Upload"
              >上传PDF文件</el-button
            >
          </el-upload>
        </el-empty>
      </div>

      <!-- PDF显示区域 -->
      <div v-show="pdfDoc" class="pdf-section">
        <el-card class="pdf-container-card" :body-style="{ padding: '0' }">
          <div
            ref="pdfContainer"
            class="pdf-container"
            @mousedown="handleMouseDown"
            @mousemove="handleMouseMove"
            @mouseup="handleMouseUp"
          >
            <!-- PDF页面将在这里动态渲染 -->
          </div>
        </el-card>
      </div>

      <!-- 批注面板 -->
      <div class="annotation-panel">
        <el-card class="annotation-card">
          <template #header>
            <div class="annotation-header-actions">
              <span>批注列表</span>
              <el-button
                v-if="annotations.length > 0"
                type="primary"
                size="small"
                @click="exportAnnotations"
              >
                导出批注
              </el-button>
            </div>
          </template>

          <div v-if="annotations.length === 0" class="empty-annotations">
            <el-empty description="暂无批注" />
          </div>

          <div v-else class="annotation-list">
            <div
              v-for="annotation in sortedAnnotations"
              :key="annotation.id"
              :class="[
                'annotation-item',
                {
                  selected: selectedAnnotationIndex === annotation.originalIndex
                }
              ]"
              @click="selectAnnotation(annotation.originalIndex)"
            >
              <div class="annotation-header">
                <span class="annotation-type">{{
                  getAnnotationTypeLabel(annotation.subtype)
                }}</span>
                <div class="annotation-actions">
                  <el-button
                    type="primary"
                    size="small"
                    text
                    @click.stop="editAnnotation(annotation.originalIndex)"
                  >
                    编辑
                  </el-button>
                  <el-button
                    type="danger"
                    size="small"
                    text
                    @click.stop="deleteAnnotation(annotation.originalIndex)"
                  >
                    删除
                  </el-button>
                </div>
              </div>

              <div class="annotation-content">
                <div
                  v-if="editingIndex === annotation.originalIndex"
                  class="editing-form"
                >
                  <el-select
                    v-model="editingType"
                    placeholder="选择批注类型"
                    style="width: 100%; margin-bottom: 8px"
                  >
                    <el-option label="参与者" value="participator" />
                    <el-option label="见证人" value="witness" />
                    <el-option label="研究者" value="researcher" />
                    <el-option label="其他" value="other" />
                  </el-select>
                  <el-input
                    v-model="editingText"
                    type="textarea"
                    :rows="2"
                    @blur="saveAnnotation(annotation.originalIndex)"
                    @keyup.enter="saveAnnotation(annotation.originalIndex)"
                  />
                </div>
                <p v-else>{{ annotation.contents || '无内容' }}</p>
              </div>

              <div class="annotation-info">
                <small>页面: {{ annotation.pageNum || 1 }}</small>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, onMounted, computed } from 'vue';
  import { ElMessage } from 'element-plus';
  import { Upload, Plus } from '@element-plus/icons-vue';

  defineOptions({ name: 'PdfPostil' });

  // Props
  const props = defineProps({
    /** PDF文件URL或文件对象 */
    modelValue: {
      type: [String, File],
      default: null
    },
    /** 是否只读模式 */
    readonly: {
      type: Boolean,
      default: false
    },
    scale: {
      type: Number,
      default: 1
    }
  });

  // Emits
  const emit = defineEmits(['update:modelValue', 'annotationsChange']);

  // 响应式数据
  const uploadRef = ref();
  const emptyUploadRef = ref();
  const pdfContainer = ref();
  const pdfDoc = ref(null);
  const annotations = ref([]);
  const selectedAnnotationIndex = ref(-1);
  const editingIndex = ref(-1);
  const editingText = ref('');
  const editingType = ref('participator');
  const annotationText = ref('');
  const annotationType = ref(null);
  const isAddingAnnotation = ref(false);
  const annotationIdCounter = ref(0);

  // 拖拽相关
  const isDragging = ref(false);
  const dragStartX = ref(0);
  const dragStartY = ref(0);
  const currentAnnotationArea = ref(null);

  // PDF.js相关
  let pdfjsLib = null;
  const cssUnits = 96 / 72;

  // 计算属性：按页面顺序排序的批注
  const sortedAnnotations = computed(() => {
    return annotations.value
      .map((annotation, index) => ({
        ...annotation,
        originalIndex: index
      }))
      .sort((a, b) => {
        // 先按页面排序，再按Y坐标排序
        if (a.pageNum !== b.pageNum) {
          return (a.pageNum || 1) - (b.pageNum || 1);
        }
        return (a.rect[1] || 0) - (b.rect[1] || 0);
      });
  });

  // 初始化PDF.js
  onMounted(async () => {
    try {
      console.log('开始初始化PDF.js...');
      // 动态加载PDF.js
      if (!window.pdfjsLib) {
        console.log('加载PDF.js库...');
        await loadPdfJs();
      }
      pdfjsLib = window.pdfjsLib;
      pdfjsLib.GlobalWorkerOptions.workerSrc =
        'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.12.313/pdf.worker.min.js';
      console.log('PDF.js初始化成功');
      ElMessage.success('PDF.js加载成功，可以上传PDF文件了');
    } catch (error) {
      console.error('PDF.js加载失败:', error);
      ElMessage.error('PDF.js加载失败: ' + error.message);
    }
  });

  // 动态加载PDF.js
  const loadPdfJs = () => {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src =
        'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.12.313/pdf.min.js';
      script.onload = resolve;
      script.onerror = reject;
      document.head.appendChild(script);
    });
  };

  // 文件上传处理
  const handleFileChange = (file) => {
    if (!file.raw) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const typedArray = new Uint8Array(e.target.result);
      loadPDF(typedArray);
    };
    reader.onerror = () => {
      ElMessage.error('文件读取失败');
    };
    reader.readAsArrayBuffer(file.raw);
  };

  // 加载PDF
  const loadPDF = async (data) => {
    try {
      ElMessage.info('正在加载PDF文件...');
      const pdf = await pdfjsLib.getDocument(data).promise;
      pdfDoc.value = pdf;
      annotations.value = [];
      selectedAnnotationIndex.value = -1;

      // 渲染所有页面
      await renderAllPages(pdf);
      ElMessage.success('PDF加载成功');
    } catch (error) {
      console.error('PDF加载错误:', error);
      ElMessage.error('PDF加载失败: ' + error.message);
    }
  };

  // 渲染所有PDF页面（平铺显示）
  const renderAllPages = async (pdf) => {
    if (!pdfContainer.value) return;

    pdfContainer.value.innerHTML = '';
    const totalPages = pdf.numPages;

    for (let pageNum = 1; pageNum <= totalPages; pageNum++) {
      try {
        const page = await pdf.getPage(pageNum);
        const viewport = page.getViewport({ scale: props.scale });

        // 创建页面容器
        const pageContainer = document.createElement('div');
        pageContainer.className = 'pdf-page-container';
        pageContainer.style.position = 'relative';
        pageContainer.style.marginBottom = '20px';
        pageContainer.dataset.pageNum = pageNum;

        // 创建canvas
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        canvas.height = Math.floor(viewport.height * cssUnits);
        canvas.width = Math.floor(viewport.width * cssUnits);

        pageContainer.appendChild(canvas);
        pdfContainer.value.appendChild(pageContainer);

        // 渲染PDF页面
        await page.render({
          transform: [cssUnits, 0, 0, cssUnits, 0, 0],
          canvasContext: context,
          viewport: viewport
        }).promise;
      } catch (error) {
        console.error(`渲染第${pageNum}页失败:`, error);
      }
    }
  };

  // 开始添加批注
  const startAddAnnotation = () => {
    if (!pdfDoc.value) {
      ElMessage.warning('请先上传PDF文件');
      return;
    }

    if (!annotationText.value.trim()) {
      ElMessage.warning('请输入批注内容');
      return;
    }

    if (!annotationType.value) {
      ElMessage.warning('请选择处理方');
      return;
    }

    isAddingAnnotation.value = true;
    pdfContainer.value.style.cursor = 'crosshair';
    ElMessage.info('请在PDF上拖动鼠标创建批注区域');
  };

  // 鼠标按下事件
  const handleMouseDown = (e) => {
    if (!isAddingAnnotation.value || e.button !== 0) return;

    e.preventDefault();
    e.stopPropagation();

    const containerRect = pdfContainer.value.getBoundingClientRect();
    const absoluteX =
      e.clientX - containerRect.left + pdfContainer.value.scrollLeft;
    const absoluteY =
      e.clientY - containerRect.top + pdfContainer.value.scrollTop;

    // 找到对应的页面
    const pageContainer = findPageContainer(absoluteY);
    if (!pageContainer) return;

    // 计算相对于页面容器的坐标
    const pageRect = pageContainer.getBoundingClientRect();
    const pageContainerRect = pdfContainer.value.getBoundingClientRect();
    const pageOffsetTop =
      pageRect.top - pageContainerRect.top + pdfContainer.value.scrollTop;

    dragStartX.value = absoluteX;
    dragStartY.value = absoluteY;

    // 创建批注区域，使用相对于页面的坐标
    const relativeX = absoluteX;
    const relativeY = absoluteY - pageOffsetTop;

    currentAnnotationArea.value = document.createElement('div');
    currentAnnotationArea.value.className = 'annotation-area';
    currentAnnotationArea.value.style.cssText = `
    position: absolute;
    left: ${relativeX}px;
    top: ${relativeY}px;
    width: 0px;
    height: 0px;
    border: 2px dashed #28a745;
    background-color: rgba(40, 167, 69, 0.1);
    z-index: 10;
    pointer-events: none;
  `;

    pageContainer.appendChild(currentAnnotationArea.value);
    isDragging.value = true;
  };

  // 鼠标移动事件
  const handleMouseMove = (e) => {
    if (!isDragging.value || !currentAnnotationArea.value) return;

    const containerRect = pdfContainer.value.getBoundingClientRect();
    const currentX =
      e.clientX - containerRect.left + pdfContainer.value.scrollLeft;
    const currentY =
      e.clientY - containerRect.top + pdfContainer.value.scrollTop;

    // 获取页面容器的偏移
    const pageContainer = currentAnnotationArea.value.parentElement;
    const pageRect = pageContainer.getBoundingClientRect();
    const pageContainerRect = pdfContainer.value.getBoundingClientRect();
    const pageOffsetTop =
      pageRect.top - pageContainerRect.top + pdfContainer.value.scrollTop;

    // 计算相对于页面的坐标
    const relativeCurrentX = currentX;
    const relativeCurrentY = currentY - pageOffsetTop;
    const relativeStartX = dragStartX.value;
    const relativeStartY = dragStartY.value - pageOffsetTop;

    const width = Math.abs(relativeCurrentX - relativeStartX);
    const height = Math.abs(relativeCurrentY - relativeStartY);
    const left = Math.min(relativeCurrentX, relativeStartX);
    const top = Math.min(relativeCurrentY, relativeStartY);

    currentAnnotationArea.value.style.left = left + 'px';
    currentAnnotationArea.value.style.top = top + 'px';
    currentAnnotationArea.value.style.width = width + 'px';
    currentAnnotationArea.value.style.height = height + 'px';
  };

  // 鼠标释放事件
  const handleMouseUp = () => {
    if (!isDragging.value || !currentAnnotationArea.value) return;

    const rect = currentAnnotationArea.value.getBoundingClientRect();

    // 检查区域大小
    if (rect.width < 20 || rect.height < 20) {
      currentAnnotationArea.value.remove();
      isDragging.value = false;
      currentAnnotationArea.value = null;
      ElMessage.warning('批注区域太小，请重新创建');
      return;
    }

    // 创建批注对象
    const pageContainer = currentAnnotationArea.value.parentElement;
    const pageNum = parseInt(pageContainer.dataset.pageNum);

    const annotation = {
      id: `annotation_${++annotationIdCounter.value}`,
      subtype: annotationType.value,
      contents: annotationText.value,
      pageNum: pageNum,
      rect: [
        parseInt(currentAnnotationArea.value.style.left),
        parseInt(currentAnnotationArea.value.style.top),
        parseInt(currentAnnotationArea.value.style.left) +
          parseInt(currentAnnotationArea.value.style.width),
        parseInt(currentAnnotationArea.value.style.top) +
          parseInt(currentAnnotationArea.value.style.height)
      ]
    };

    annotations.value.push(annotation);

    // 更新批注区域样式
    updateAnnotationAreaStyle(
      currentAnnotationArea.value,
      annotation,
      annotations.value.length - 1
    );

    // 重置状态
    isDragging.value = false;
    currentAnnotationArea.value = null;
    isAddingAnnotation.value = false;
    pdfContainer.value.style.cursor = 'default';
    annotationText.value = '';
    annotationType.value = null;

    ElMessage.success('批注添加成功');
    emit('annotationsChange', annotations.value);
  };

  // 找到对应的页面容器
  const findPageContainer = (y) => {
    const pageContainers = pdfContainer.value.querySelectorAll(
      '.pdf-page-container'
    );
    for (const container of pageContainers) {
      const rect = container.getBoundingClientRect();
      const containerRect = pdfContainer.value.getBoundingClientRect();
      const relativeTop =
        rect.top - containerRect.top + pdfContainer.value.scrollTop;
      const relativeBottom = relativeTop + rect.height;

      if (y >= relativeTop && y <= relativeBottom) {
        return container;
      }
    }
    return pageContainers[0]; // 默认返回第一页
  };

  // 更新批注区域样式
  const updateAnnotationAreaStyle = (area, annotation, index) => {
    area.style.cssText = `
    position: absolute;
    left: ${annotation.rect[0]}px;
    top: ${annotation.rect[1]}px;
    width: ${annotation.rect[2] - annotation.rect[0]}px;
    height: ${annotation.rect[3] - annotation.rect[1]}px;
    border: 2px solid #28a745;
    background-color: rgba(40, 167, 69, 0.1);
    z-index: 10;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #28a745;
    font-weight: bold;
  `;

    area.innerHTML = `<span>${annotation.contents}</span>`;
    area.dataset.annotationIndex = index;

    // 添加点击事件
    area.addEventListener('click', () => {
      selectAnnotation(index);
    });
  };

  // 选择批注
  const selectAnnotation = (index) => {
    selectedAnnotationIndex.value = index;

    // 清除所有选中状态
    const allAreas = pdfContainer.value.querySelectorAll('.annotation-area');
    allAreas.forEach((area) => {
      area.style.border = '2px solid #28a745';
      area.style.backgroundColor = 'rgba(40, 167, 69, 0.1)';
    });

    // 高亮选中的批注
    const selectedArea = pdfContainer.value.querySelector(
      `[data-annotation-index="${index}"]`
    );
    if (selectedArea) {
      selectedArea.style.border = '2px solid #f00';
      selectedArea.style.backgroundColor = 'rgba(255, 0, 0, 0.2)';

      // 自动滚动到批注位置
      const annotation = annotations.value[index];
      if (annotation) {
        // 计算批注在容器中的位置
        const rect = selectedArea.getBoundingClientRect();
        const containerRect = pdfContainer.value.getBoundingClientRect();

        // 滚动到批注位置，居中显示
        const scrollTop = pdfContainer.value.scrollTop;
        const targetScrollTop =
          scrollTop + (rect.top - containerRect.top) - containerRect.height / 2;

        pdfContainer.value.scrollTo({
          top: Math.max(0, targetScrollTop),
          behavior: 'smooth'
        });
      }
    }
  };

  // 编辑批注
  const editAnnotation = (index) => {
    editingIndex.value = index;
    editingText.value = annotations.value[index].contents;
    editingType.value = annotations.value[index].subtype;
  };

  // 保存批注
  const saveAnnotation = (index) => {
    if (editingText.value.trim()) {
      annotations.value[index].contents = editingText.value.trim();
      annotations.value[index].subtype = editingType.value;

      // 更新PDF上的显示
      const area = pdfContainer.value.querySelector(
        `[data-annotation-index="${index}"]`
      );
      if (area) {
        area.innerHTML = `<span>${editingText.value.trim()}</span>`;
      }

      emit('annotationsChange', annotations.value);
      ElMessage.success('批注更新成功');
    }

    editingIndex.value = -1;
    editingText.value = '';
    editingType.value = 'participator';
  };

  // 删除批注
  const deleteAnnotation = (index) => {
    // 从PDF上移除批注区域
    const area = pdfContainer.value.querySelector(
      `[data-annotation-index="${index}"]`
    );
    if (area) {
      area.remove();
    }

    // 从数组中移除
    annotations.value.splice(index, 1);

    // 重新设置索引
    const allAreas = pdfContainer.value.querySelectorAll('.annotation-area');
    allAreas.forEach((area, idx) => {
      area.dataset.annotationIndex = idx;
    });

    selectedAnnotationIndex.value = -1;
    emit('annotationsChange', annotations.value);
    ElMessage.success('批注删除成功');
  };

  // 获取批注类型标签
  const getAnnotationTypeLabel = (type) => {
    const typeMap = {
      participator: '参与者',
      researcher: '研究者'
    };
    return typeMap[type] || type;
  };

  // 导出批注
  const exportAnnotations = () => {
    if (annotations.value.length === 0) {
      ElMessage.warning('暂无批注可导出');
      return;
    }

    const exportData = {
      exportTime: new Date().toISOString(),
      totalAnnotations: annotations.value.length,
      annotations: annotations.value.map((annotation, index) => ({
        id: annotation.id,
        index: index + 1,
        type: annotation.subtype,
        typeLabel: getAnnotationTypeLabel(annotation.subtype),
        content: annotation.contents,
        pageNumber: annotation.pageNum,
        position: {
          x: annotation.rect[0],
          y: annotation.rect[1],
          width: annotation.rect[2] - annotation.rect[0],
          height: annotation.rect[3] - annotation.rect[1]
        },
        normalized: {
          x: Math.round((annotation.rect[0] / 800) * 5000).toString(),
          y: Math.round((annotation.rect[1] / 600) * 5000).toString(),
          width: Math.round(
            (annotation.rect[2] - annotation.rect[0]) * 0.353
          ).toString(),
          height: Math.round(
            (annotation.rect[3] - annotation.rect[1]) * 0.353
          ).toString(),
          basePoint: 'LU'
        }
      }))
    };

    // 创建下载链接
    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `pdf_annotations_${new Date().getTime()}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    ElMessage.success(`成功导出 ${annotations.value.length} 个批注`);
  };
</script>

<style lang="scss" scoped>
  .pdf-postil-container {
    height: 100%;
    display: flex;
    flex-direction: column;

    .toolbar {
      padding: 16px;
      background: #f5f5f5;
      border-bottom: 1px solid #e0e0e0;
      display: flex;
      align-items: center;
      gap: 10px;
      flex-wrap: wrap;
    }

    .main-content {
      flex: 1;
      display: flex;
      height: calc(100vh - 120px);
      overflow: auto;

      .empty-state {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
      }

      .pdf-section {
        flex: 1;
        margin-right: 8px;

        .pdf-container-card {
          height: calc(100% - 2px);
          overflow: auto;
          .pdf-container {
            height: 100%;
            overflow: auto;
            position: relative;
            background: #f8f9fa;

            .pdf-page-container {
              position: relative;
              margin-bottom: 20px;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
              background: white;
              display: flex;
              justify-content: center;

              canvas {
                display: block;
                max-width: 100%;
                height: auto;
              }
            }
          }
        }
      }

      .annotation-panel {
        width: 320px;
        flex-shrink: 0;
        position: sticky;
        top: 0;
        height: 100%;
        max-height: calc(100vh - 120px);

        .annotation-card {
          height: calc(100% - 2px);

          .annotation-header-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 100%;
          }

          .empty-annotations {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 200px;
          }

          .annotation-list {
            max-height: calc(100vh - 300px);
            overflow-y: auto;

            .annotation-item {
              border: 1px solid #e0e0e0;
              border-radius: 6px;
              padding: 12px;
              margin-bottom: 12px;
              cursor: pointer;
              transition: all 0.3s ease;

              &:hover {
                border-color: #409eff;
                box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
              }

              &.selected {
                border-color: #409eff;
                background-color: #f0f8ff;
                box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
              }

              .annotation-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;

                .annotation-type {
                  font-size: 12px;
                  color: #666;
                  background: #f0f0f0;
                  padding: 2px 6px;
                  border-radius: 3px;
                }

                .annotation-actions {
                  display: flex;
                  gap: 4px;
                }
              }

              .annotation-content {
                margin-bottom: 8px;

                .editing-form {
                  .el-select {
                    margin-bottom: 8px;
                  }
                }

                p {
                  margin: 0;
                  font-size: 14px;
                  line-height: 1.4;
                  color: #333;
                  word-break: break-word;
                }
              }

              .annotation-info {
                font-size: 12px;
                color: #999;
              }
            }
          }
        }
      }
    }
  }

  // 批注区域样式（全局样式，因为动态创建的元素不在scoped范围内）
  :global(.annotation-area) {
    position: absolute;
    border: 2px solid #28a745;
    background-color: rgba(40, 167, 69, 0.1);
    z-index: 10;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #28a745;
    font-weight: bold;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);

    span {
      background-color: rgba(255, 255, 255, 0.9);
      padding: 2px 6px;
      border-radius: 3px;
      max-width: 100%;
      max-height: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    &:hover {
      border-color: #1e7e34;
      background-color: rgba(30, 126, 52, 0.15);
    }
  }

  :global(.annotation-area.selected) {
    border: 2px solid #f00 !important;
    background-color: rgba(255, 0, 0, 0.2) !important;
  }
</style>
