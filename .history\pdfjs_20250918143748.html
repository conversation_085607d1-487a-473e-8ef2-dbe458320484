<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF批注工具</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.12.313/pdf.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        #pdf-container {
            position: relative;
            border: 1px solid #ccc;
            margin: 20px 0;
            overflow: auto;
            max-height: 800px;
        }
        .annotation-marker {
            position: absolute;
            width: 10px;
            height: 10px;
            background-color: red;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            cursor: move;
            z-index: 10;
        }
        .annotation-area {
            position: absolute;
            border: 1px dashed #00f;
            background-color: rgba(0, 0, 255, 0.1);
            z-index: 5;
            cursor: move;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #333;
            text-align: center;
            overflow: hidden;
            word-wrap: break-word;
            padding: 2px;
            box-sizing: border-box;
        }
        .annotation-area.selected {
            border: 2px solid #f00;
            background-color: rgba(255, 0, 0, 0.2);
        }
        .annotation-content {
            max-width: 100%;
            max-height: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .resize-handle {
            position: absolute;
            width: 10px;
            height: 10px;
            background-color: #fff;
            border: 1px solid #000;
            z-index: 15;
        }
        .resize-handle-nw {
            top: -5px;
            left: -5px;
            cursor: nw-resize;
        }
        .resize-handle-ne {
            top: -5px;
            right: -5px;
            cursor: ne-resize;
        }
        .resize-handle-sw {
            bottom: -5px;
            left: -5px;
            cursor: sw-resize;
        }
        .resize-handle-se {
            bottom: -5px;
            right: -5px;
            cursor: se-resize;
        }
        #coordinates-display {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            background-color: #f9f9f9;
        }
        .status-message {
            color: #d9534f;
            margin: 10px 0;
        }
        .toolbar {
            margin: 10px 0;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            align-items: center;
        }
        #annotation-text {
            width: 200px;
            padding: 5px;
        }
        button {
            padding: 5px 10px;
            cursor: pointer;
        }
        .annotation-type-selector {
            margin-left: 10px;
        }
        .annotation-actions {
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>PDF批注工具</h1>
        
        <div class="toolbar">
            <input type="file" id="pdf-upload" accept=".pdf">
            <button id="add-annotation">添加批注</button>
            <button id="delete-annotation" disabled>删除批注</button>
            <button id="get-coordinates">获取批注坐标</button>
            <button id="export-all">导出所有批注</button>
            <input type="text" id="annotation-text" placeholder="输入批注内容">
            <select id="annotation-type" class="annotation-type-selector">
                <option value="Text">文本批注</option>
                <option value="Highlight">高亮批注</option>
                <option value="Underline">下划线批注</option>
                <option value="StrikeOut">删除线批注</option>
            </select>
            <div id="status-message" class="status-message"></div>
        </div>
        
        <div id="pdf-container"></div>
        
        <div id="coordinates-display">
            <h3>批注信息</h3>
            <pre id="coordinates-data">请先上传PDF文件</pre>
            <div class="annotation-actions">
                <button id="copy-coordinates">复制坐标</button>
                <button id="clear-display">清空显示</button>
            </div>
        </div>
    </div>

    <script>
        // 初始化PDF.js
        pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.12.313/pdf.worker.min.js';

        // 全局变量
        let pdfDoc = null;
        let currentPage = 1;
        let scale = 1.5;
        let annotations = [];
        let isAddingAnnotation = false;
        let currentAnnotationArea = null;
        let viewport = null;
        let selectedAnnotation = null;
        let selectedAnnotationIndex = -1;
        let isDragging = false;
        let isResizing = false;
        let resizeHandle = null;
        let dragStartX = 0;
        let dragStartY = 0;
        let originalRect = null;
        let annotationIdCounter = 0;

        // DOM元素
        const pdfUpload = document.getElementById('pdf-upload');
        const pdfContainer = document.getElementById('pdf-container');
        const addAnnotationBtn = document.getElementById('add-annotation');
        const deleteAnnotationBtn = document.getElementById('delete-annotation');
        const getCoordinatesBtn = document.getElementById('get-coordinates');
        const annotationTextInput = document.getElementById('annotation-text');
        const annotationTypeSelect = document.getElementById('annotation-type');
        const coordinatesData = document.getElementById('coordinates-data');
        const statusMessage = document.getElementById('status-message');
        const copyCoordinatesBtn = document.getElementById('copy-coordinates');
        const clearDisplayBtn = document.getElementById('clear-display');
        const exportAllBtn = document.getElementById('export-all');

        // 上传PDF文件
        pdfUpload.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                statusMessage.textContent = '正在加载PDF文件...';
                const fileReader = new FileReader();
                fileReader.onload = function() {
                    const typedArray = new Uint8Array(this.result);
                    loadPDF(typedArray);
                };
                fileReader.onerror = function() {
                    statusMessage.textContent = '文件读取失败';
                };
                fileReader.readAsArrayBuffer(file);
            }
        });

        // 加载PDF
        function loadPDF(data) {
            console.log('开始加载PDF...');
            pdfjsLib.getDocument(data).promise.then(function(pdf) {
                console.log('PDF加载成功');
                statusMessage.textContent = 'PDF加载成功，正在解析批注...';
                pdfDoc = pdf;
                renderPage(currentPage);
            }).catch(function(error) {
                console.error('PDF加载错误:', error);
                statusMessage.textContent = 'PDF加载失败: ' + error.message;
            });
        }

        // 渲染PDF页面
        function renderPage(pageNum) {
            pdfContainer.innerHTML = '';
            annotations = [];
            selectedAnnotation = null;
            deleteAnnotationBtn.disabled = true;
            
            pdfDoc.getPage(pageNum).then(function(page) {
                console.log('获取页面成功，开始获取批注...');
                viewport = page.getViewport({ scale: scale });
                const canvas = document.createElement('canvas');
                const context = canvas.getContext('2d');
                canvas.height = viewport.height;
                canvas.width = viewport.width;
                
                pdfContainer.appendChild(canvas);
                
                // 渲染PDF页面
                page.render({
                    canvasContext: context,
                    viewport: viewport
                });
                
                // 设置PDF容器大小
                pdfContainer.style.width = viewport.width + 'px';
                pdfContainer.style.height = viewport.height + 'px';
                
                // 获取批注
                page.getAnnotations().then(function(annots) {
                    console.log('获取到的批注:', annots);
                    annotations = annots.filter(annot => annot.rect); // 只保留有坐标的批注
                    
                    if (annotations.length === 0) {
                        statusMessage.textContent = '未检测到任何批注';
                        console.warn('未找到有效批注');
                    } else {
                        statusMessage.textContent = `已找到 ${annotations.length} 个批注`;
                    }
                    
                    displayAnnotations(annotations, viewport);
                }).catch(err => {
                    console.error('获取批注失败:', err);
                    statusMessage.textContent = '获取批注时出错: ' + err.message;
                });
            }).catch(err => {
                console.error('渲染页面失败:', err);
                statusMessage.textContent = '渲染页面时出错: ' + err.message;
            });
        }

        // 显示批注标记
        function displayAnnotations(annots, viewport) {
            annots.forEach((annot, index) => {
                if (annot.rect) {
                    const rect = viewport.convertToViewportRectangle(annot.rect);
                    const left = rect[0];
                    const top = rect[1];
                    const width = rect[2] - rect[0];
                    const height = rect[3] - rect[1];
                    
                    // 创建批注区域
                    const area = createAnnotationArea(index, left, top, width, height, annot.subtype, annot.contents);
                    
                    // 创建标记点
                    const marker = createAnnotationMarker(index, left + width/2, top + height/2);
                    
                    // 添加拖拽和缩放功能
                    addDragAndResizeHandlers(area, marker, index);
                    
                    pdfContainer.appendChild(area);
                    pdfContainer.appendChild(marker);
                }
            });
            
            // 点击PDF空白处取消选择
            pdfContainer.addEventListener('click', function(e) {
                if (e.target === pdfContainer) {
                    clearSelection();
                }
            });
        }

        // 创建批注区域
        function createAnnotationArea(index, left, top, width, height, type, contents) {
            const area = document.createElement('div');
            area.className = 'annotation-area';
            area.dataset.annotationIndex = index;
            area.style.left = left + 'px';
            area.style.top = top + 'px';
            area.style.width = width + 'px';
            area.style.height = height + 'px';
            area.title = `类型: ${type}\n内容: ${contents || '无'}`;
            
            // 添加缩放控制点
            const resizeHandles = [
                createResizeHandle('nw', 'nw-resize'),
                createResizeHandle('ne', 'ne-resize'),
                createResizeHandle('sw', 'sw-resize'),
                createResizeHandle('se', 'se-resize')
            ];
            
            resizeHandles.forEach(handle => {
                area.appendChild(handle);
            });
            
            return area;
        }

        // 创建缩放控制点
        function createResizeHandle(position, cursor) {
            const handle = document.createElement('div');
            handle.className = `resize-handle resize-handle-${position}`;
            handle.style.cursor = cursor;
            return handle;
        }

        // 创建批注标记点
        function createAnnotationMarker(index, left, top) {
            const marker = document.createElement('div');
            marker.className = 'annotation-marker';
            marker.dataset.annotationIndex = index;
            marker.style.left = left + 'px';
            marker.style.top = top + 'px';
            return marker;
        }

        // 添加拖拽和缩放事件处理
        function addDragAndResizeHandlers(area, marker, index) {
            // 点击事件 - 选择批注
            const clickHandler = function(e) {
                e.stopPropagation();
                selectAnnotation(index);
            };
            
            area.addEventListener('click', clickHandler);
            marker.addEventListener('click', clickHandler);
            
            // 拖拽事件 - 区域和标记点
            [area, marker].forEach(element => {
                element.addEventListener('mousedown', function(e) {
                    if (e.button !== 0) return;
                    
                    const annotation = annotations[index];
                    if (!annotation) return;
                    
                    if (e.target.classList.contains('resize-handle')) {
                        // 缩放操作
                        isResizing = true;
                        resizeHandle = e.target;
                        originalRect = {
                            left: parseFloat(area.style.left),
                            top: parseFloat(area.style.top),
                            width: parseFloat(area.style.width),
                            height: parseFloat(area.style.height)
                        };
                    } else {
                        // 拖拽操作
                        isDragging = true;
                        dragStartX = e.clientX;
                        dragStartY = e.clientY;
                        originalRect = {
                            left: parseFloat(area.style.left),
                            top: parseFloat(area.style.top)
                        };
                    }
                    
                    e.preventDefault();
                });
            });
            
            // 移动事件
            document.addEventListener('mousemove', function(e) {
                if (isDragging && selectedAnnotation) {
                    const dx = e.clientX - dragStartX;
                    const dy = e.clientY - dragStartY;
                    
                    const newLeft = originalRect.left + dx;
                    const newTop = originalRect.top + dy;
                    
                    // 更新位置
                    area.style.left = newLeft + 'px';
                    area.style.top = newTop + 'px';
                    marker.style.left = (newLeft + parseFloat(area.style.width)/2) + 'px';
                    marker.style.top = (newTop + parseFloat(area.style.height)/2) + 'px';
                    
                    // 更新批注坐标
                    updateAnnotationPosition(index, newLeft, newTop);
                }
                
                if (isResizing && selectedAnnotation) {
                    const dx = e.clientX - dragStartX;
                    const dy = e.clientY - dragStartY;
                    
                    let newWidth = originalRect.width;
                    let newHeight = originalRect.height;
                    let newLeft = originalRect.left;
                    let newTop = originalRect.top;
                    
                    // 根据不同的控制点调整大小
                    if (resizeHandle.classList.contains('resize-handle-nw')) {
                        newWidth = originalRect.width - dx;
                        newHeight = originalRect.height - dy;
                        newLeft = originalRect.left + dx;
                        newTop = originalRect.top + dy;
                    } else if (resizeHandle.classList.contains('resize-handle-ne')) {
                        newWidth = originalRect.width + dx;
                        newHeight = originalRect.height - dy;
                        newTop = originalRect.top + dy;
                    } else if (resizeHandle.classList.contains('resize-handle-sw')) {
                        newWidth = originalRect.width - dx;
                        newHeight = originalRect.height + dy;
                        newLeft = originalRect.left + dx;
                    } else if (resizeHandle.classList.contains('resize-handle-se')) {
                        newWidth = originalRect.width + dx;
                        newHeight = originalRect.height + dy;
                    }
                    
                    // 确保最小尺寸
                    newWidth = Math.max(20, newWidth);
                    newHeight = Math.max(20, newHeight);
                    
                    // 更新大小和位置
                    area.style.width = newWidth + 'px';
                    area.style.height = newHeight + 'px';
                    area.style.left = newLeft + 'px';
                    area.style.top = newTop + 'px';
                    marker.style.left = (newLeft + newWidth/2) + 'px';
                    marker.style.top = (newTop + newHeight/2) + 'px';
                    
                    // 更新批注坐标
                    updateAnnotationSize(index, newLeft, newTop, newWidth, newHeight);
                }
            });
            
            // 鼠标释放事件
            document.addEventListener('mouseup', function() {
                isDragging = false;
                isResizing = false;
                resizeHandle = null;
                originalRect = null;
            });
        }

        // 更新批注位置
        function updateAnnotationPosition(index, left, top) {
            const annotation = annotations[index];
            if (!annotation) return;
            
            const width = annotation.rect[2] - annotation.rect[0];
            const height = annotation.rect[3] - annotation.rect[1];
            
            annotation.rect = [left, top, left + width, top + height];
            
            // 更新显示
            showAnnotationDetails(annotation, viewport);
        }

        // 更新批注大小
        function updateAnnotationSize(index, left, top, width, height) {
            const annotation = annotations[index];
            if (!annotation) return;
            
            annotation.rect = [left, top, left + width, top + height];
            
            // 更新显示
            showAnnotationDetails(annotation, viewport);
        }

        // 选择批注
        function selectAnnotation(index) {
            clearSelection();
            
            selectedAnnotation = annotations[index];
            deleteAnnotationBtn.disabled = false;
            
            // 高亮选中的批注
            const areas = document.querySelectorAll('.annotation-area');
            const markers = document.querySelectorAll('.annotation-marker');
            
            if (areas[index]) {
                areas[index].classList.add('selected');
            }
            
            // 显示批注详情
            showAnnotationDetails(selectedAnnotation, viewport);
        }

        // 清除选择
        function clearSelection() {
            const selectedAreas = document.querySelectorAll('.annotation-area.selected');
            selectedAreas.forEach(area => {
                area.classList.remove('selected');
            });
            
            selectedAnnotation = null;
            deleteAnnotationBtn.disabled = true;
        }

        // 显示批注详细信息
        function showAnnotationDetails(annot, viewport) {
            const rect = viewport.convertToViewportRectangle(annot.rect);
            
            const details = {
                type: annot.subtype,
                contents: annot.contents || '无',
                coordinates: {
                    pdf: annot.rect,
                    viewport: rect,
                    normalized: {
                        x: annot.rect[0] / viewport.width,
                        y: annot.rect[1] / viewport.height,
                        width: (annot.rect[2] - annot.rect[0]) / viewport.width,
                        height: (annot.rect[3] - annot.rect[1]) / viewport.height
                    }
                },
                additionalData: {
                    author: annot.title || '未知',
                    creationDate: annot.modificationDate || annot.creationDate || '未知',
                    color: annot.color || '默认'
                }
            };
            
            coordinatesData.textContent = JSON.stringify(details, null, 2);
        }

        // 添加批注
        addAnnotationBtn.addEventListener('click', function() {
            if (!pdfDoc) {
                alert('请先上传PDF文件');
                return;
            }
            
            const text = annotationTextInput.value.trim();
            if (!text) {
                alert('请输入批注内容');
                return;
            }
            
            isAddingAnnotation = true;
            pdfContainer.style.cursor = 'crosshair';
            statusMessage.textContent = '请在PDF上拖动鼠标创建批注区域';
            clearSelection();
        });

        // 处理批注区域创建
        pdfContainer.addEventListener('mousedown', function(e) {
            if (!isAddingAnnotation || e.button !== 0) return;
            
            e.stopPropagation();
            
            const startX = e.offsetX;
            const startY = e.offsetY;
            
            // 创建批注区域
            currentAnnotationArea = document.createElement('div');
            currentAnnotationArea.className = 'annotation-area';
            currentAnnotationArea.style.left = startX + 'px';
            currentAnnotationArea.style.top = startY + 'px';
            currentAnnotationArea.style.width = '0px';
            currentAnnotationArea.style.height = '0px';
            pdfContainer.appendChild(currentAnnotationArea);
            
            // 添加缩放控制点
            const resizeHandles = [
                createResizeHandle('nw', 'nw-resize'),
                createResizeHandle('ne', 'ne-resize'),
                createResizeHandle('sw', 'sw-resize'),
                createResizeHandle('se', 'se-resize')
            ];
            
            resizeHandles.forEach(handle => {
                currentAnnotationArea.appendChild(handle);
            });
            
            function moveHandler(e) {
                const width = e.offsetX - startX;
                const height = e.offsetY - startY;
                currentAnnotationArea.style.width = Math.abs(width) + 'px';
                currentAnnotationArea.style.height = Math.abs(height) + 'px';
                if (width < 0) {
                    currentAnnotationArea.style.left = e.offsetX + 'px';
                }
                if (height < 0) {
                    currentAnnotationArea.style.top = e.offsetY + 'px';
                }
            }
            
            function upHandler(e) {
                document.removeEventListener('mousemove', moveHandler);
                document.removeEventListener('mouseup', upHandler);
                
                // 确保批注区域有最小尺寸
                if (parseInt(currentAnnotationArea.style.width) < 20) {
                    currentAnnotationArea.style.width = '20px';
                }
                if (parseInt(currentAnnotationArea.style.height) < 20) {
                    currentAnnotationArea.style.height = '20px';
                }
                
                // 确认添加批注
                const confirmAdd = confirm('确定在此位置添加批注吗？');
                if (confirmAdd) {
                    addNewAnnotation(currentAnnotationArea);
                } else {
                    pdfContainer.removeChild(currentAnnotationArea);
                }
                
                isAddingAnnotation = false;
                pdfContainer.style.cursor = '';
                currentAnnotationArea = null;
            }
            
            document.addEventListener('mousemove', moveHandler);
            document.addEventListener('mouseup', upHandler, { once: true });
        });

        // 添加新批注
        function addNewAnnotation(area) {
            const rect = area.getBoundingClientRect();
            const containerRect = pdfContainer.getBoundingClientRect();
            
            // 计算相对于PDF容器的坐标
            const left = rect.left - containerRect.left;
            const top = rect.top - containerRect.top;
            const width = rect.width;
            const height = rect.height;
            
            // 创建批注对象
            const newAnnotation = {
                subtype: annotationTypeSelect.value,
                rect: [left, top, left + width, top + height],
                contents: annotationTextInput.value,
                title: '用户添加',
                color: [0, 0, 1], // 蓝色
                creationDate: new Date().toISOString()
            };
            
            // 添加到批注列表
            annotations.push(newAnnotation);
            const index = annotations.length - 1;
            
            // 创建标记点
            const marker = createAnnotationMarker(index, left + width/2, top + height/2);
            
            // 添加拖拽和缩放功能
            addDragAndResizeHandlers(area, marker, index);
            
            pdfContainer.appendChild(marker);
            
            // 选中新添加的批注
            selectAnnotation(index);
            
            statusMessage.textContent = '批注添加成功';
            console.log('新批注:', newAnnotation);
            
            // 清空输入框
            annotationTextInput.value = '';
        }

        // 删除批注
        deleteAnnotationBtn.addEventListener('click', function() {
            if (!selectedAnnotation) return;
            
            const confirmDelete = confirm('确定要删除这个批注吗？');
            if (confirmDelete) {
                const index = annotations.indexOf(selectedAnnotation);
                if (index !== -1) {
                    annotations.splice(index, 1);
                    
                    // 重新渲染页面
                    renderPage(currentPage);
                    
                    statusMessage.textContent = '批注已删除';
                }
            }
        });

        // 获取批注坐标
        getCoordinatesBtn.addEventListener('click', function() {
            if (!pdfDoc) {
                alert('请先上传PDF文件');
                return;
            }
            
            console.log('当前批注数量:', annotations.length);
            
            if (annotations.length === 0) {
                alert('该PDF文件中未检测到任何批注');
                coordinatesData.textContent = '未找到批注信息';
                return;
            }
            
            // 收集所有批注的坐标信息
            const allAnnotations = annotations.map(annot => {
                return {
                    type: annot.subtype,
                    contents: annot.contents || '无',
                    coordinates: annot.rect,
                    page: currentPage
                };
            });
            
            coordinatesData.textContent = JSON.stringify(allAnnotations, null, 2);
            console.log('所有批注坐标:', allAnnotations);
        });

        // 复制坐标
        copyCoordinatesBtn.addEventListener('click', function() {
            if (!coordinatesData.textContent || coordinatesData.textContent === '请先上传PDF文件') {
                alert('没有可复制的数据');
                return;
            }
            
            navigator.clipboard.writeText(coordinatesData.textContent)
                .then(() => {
                    statusMessage.textContent = '坐标已复制到剪贴板';
                    setTimeout(() => statusMessage.textContent = '', 2000);
                })
                .catch(err => {
                    console.error('复制失败:', err);
                    statusMessage.textContent = '复制失败: ' + err.message;
                });
        });

        // 清空显示
        clearDisplayBtn.addEventListener('click', function() {
            coordinatesData.textContent = '';
        });
    </script>
</body>
</html>