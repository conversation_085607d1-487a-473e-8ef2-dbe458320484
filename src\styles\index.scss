/** 全局样式 */
@use 'element-plus/theme-chalk/src/mixins/function.scss' as *;
@use './transition.scss' as *;

* {
  outline: none;
}

html {
  overflow: auto;
  height: 100%;
}

body {
  margin: 0;
  line-height: 1.58;
  color: getCssVar('text-color', 'regular');
  font-size: getCssVar('font-size', 'base');
  font-family: getCssVar('font-family');
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  overflow-x: hidden;
  overflow-y: auto;
  height: 100%;
}

/* 关闭响应式 */
body.ele-body-limited {
  min-width: 1200px;
}

/* 色弱模式 */
.ele-admin-weak {
  filter: invert(0.8);
}

/* 按钮加图标减少间距 */
.ele-btn-icon.el-button,
.ele-btn-icon.el-button.is-round {
  padding-left: 12px;
  padding-right: 12px;

  & > .el-icon {
    margin-left: -2px;
    margin-right: -2px;
  }

  &.el-button--small {
    padding-left: 6px;
    padding-right: 6px;
  }

  &.el-button--large {
    padding-left: 16px;
    padding-right: 16px;
  }
}

/* 级联选择器增加高度 */
.ele-popper-higher .el-cascader-menu__wrap.el-scrollbar__wrap {
  height: 280px;
}

/* 间距组件样式优化 */
.el-space--horizontal > .el-space__item:last-child {
  margin-right: 0 !important;
}

.el-space--vertical > .el-space__item:last-child {
  padding-bottom: 0 !important;
}

/* echarts */
.echarts > div > div {
  max-width: 100%;
  overflow: hidden;
}

/* 小屏幕时分页去掉一些组件 */
@media screen and (max-width: 768px) {
  .ele-pro-table .el-pagination {
    .el-pagination__sizes,
    .el-pagination__jump {
      display: none;
    }
  }
}

/* 表单验证气泡形式 */
.pro-form-error-popper.el-form-item > .el-form-item__content {
  & > .el-form-item__error {
    position: absolute;
    left: 0;
    top: auto;
    bottom: calc(100% + 3px);
    width: max-content;
    color: #fff;
    font-size: 12px;
    background: getCssVar('color-danger');
    transition: all 0.2s;
    padding: 4px 6px;
    border-radius: 3px;
    z-index: 999;
    transform: none;

    &:after {
      content: '';
      border: 4px solid transparent;
      border-top-color: getCssVar('color-danger');
      position: absolute;
      left: 8px;
      bottom: -7px;
    }
  }
}

/* 主题切换过渡 */
::view-transition-old(root),
::view-transition-new(root),
::view-transition-old(body),
::view-transition-new(body) {
  animation: none;
  mix-blend-mode: normal;
}

::view-transition-new(root),
html.dark::view-transition-old(root),
::view-transition-new(body),
html:has(> body.ele-admin-weak)::view-transition-old(body) {
  z-index: 1;
}

::view-transition-old(root),
html.dark::view-transition-new(root),
::view-transition-old(body),
html:has(> body.ele-admin-weak)::view-transition-new(body) {
  z-index: 2147483646;
}

html.disabled-transition,
html.disabled-transition :not(.view-transition-trigger) * {
  transition: none !important;
}
