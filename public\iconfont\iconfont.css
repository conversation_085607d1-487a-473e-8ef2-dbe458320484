@font-face {
  font-family: "iconfont"; /* Project id 4869468 */
  src: url('iconfont.woff2?t=1742866543017') format('woff2'),
       url('iconfont.woff?t=1742866543017') format('woff'),
       url('iconfont.ttf?t=1742866543017') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-shang:before {
  content: "\e607";
}

.icon-xia:before {
  content: "\e60c";
}

.icon-descending1:before {
  content: "\e64c";
}

.icon-ascending1:before {
  content: "\e645";
}

.icon-direction-down:before {
  content: "\e66b";
}

.icon-direction-up:before {
  content: "\e66c";
}

