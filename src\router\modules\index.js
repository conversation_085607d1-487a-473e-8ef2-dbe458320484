export default [
  {
    path: '/index',
    component: '/workplace/index',
    meta: { title: '首页', icon: 'IconProDesktopOutlined' }
  },
  {
    path: '/menu-change',
    component: '/menu-change',
    meta: { title: '菜单切换', icon: 'IconProSwapOutlined' }
  },
  {
    path: '/icon-demo',
    component: '/icon-demo',
    meta: {
      title: 'icon示例',
      icon: 'IconProBookOutlined',
      hide: false,
      lang: {
        zh_TW: 'icon示例',
        en: 'icon Demo'
      }
    }
  },
  {
    path: '/table',
    meta: {
      title: '列表页面',
      icon: 'IconProTableOutlined',
      hide: false,
      props: {
        hideTimeout: 450
      },
      lang: {
        zh_TW: '清單頁面',
        en: 'List'
      }
    },
    children: [
      {
        path: '/table/normal-table',
        component: '/table/normal-table',
        meta: {
          title: '基础表格',
          icon: 'IconProTableOutlined',
          hide: false,
          lang: {
            zh_TW: '基礎表格',
            en: 'Basic Table'
          }
        }
      },
      {
        path: '/table/normal-table-demo',
        component: '/table/normal-table-demo',
        meta: {
          title: '表格示例',
          icon: 'IconProDatabaseOutlined',
          hide: false,
          lang: {
            zh_TW: '表格示例',
            en: 'Table Demo'
          }
        }
      }
    ]
  },
  {
    path: '/result',
    meta: {
      title: '结果页面',
      icon: 'IconProCheckCircleOutlined',
      hide: false,
      lang: {
        zh_TW: '結果頁面',
        en: 'Result'
      }
    },
    children: [
      {
        path: '/result/success',
        component: '/result/success',
        meta: {
          title: '成功页',
          icon: 'IconProLinkOutlined',
          hide: false,
          lang: {
            zh_TW: '成功頁',
            en: 'Success'
          }
        }
      },
      {
        path: '/result/fail',
        component: '/result/fail',
        meta: {
          title: '失败页',
          icon: 'IconProLinkOutlined',
          hide: false,
          lang: {
            zh_TW: '失敗頁',
            en: 'Fail'
          }
        }
      }
    ]
  },
  {
    path: '/exception',
    meta: {
      title: '异常页面',
      icon: 'IconProWarningOutlined',
      hide: false,
      lang: {
        zh_TW: '异常頁面',
        en: 'Exception'
      }
    },
    redirect: '/exception/403',
    children: [
      {
        path: '/exception/403',
        component: '/exception/403',
        meta: {
          title: '403',
          icon: 'IconProLinkOutlined',
          hide: false
        }
      },
      {
        path: '/exception/404',
        component: '/exception/404',
        meta: {
          title: '404',
          icon: 'IconProLinkOutlined',
          hide: false
        }
      },
      {
        path: '/exception/500',
        component: '/exception/500',
        meta: {
          title: '500',
          icon: 'IconProLinkOutlined',
          hide: false
        }
      }
    ]
  },
  {
    path: '/tenant',
    meta: {
      hide: false,
      keepAlive: true,
      title: '租户管理',
      icon: 'IconProDatabaseOutlined',
      noCache: false,
      link: null
    },
    redirect: 'tenant',
    children: [
      {
        path: '/tenant/tenant',
        component: '/system/tenant/index',
        meta: {
          hide: false,
          keepAlive: true,
          title: '租户管理',
          icon: null,
          noCache: false,
          link: null
        }
      },
      {
        path: '/tenant/tenantPackage',
        component: '/system/tenantPackage/index',
        meta: {
          hide: false,
          keepAlive: true,
          title: '租户套餐管理',
          icon: null,
          noCache: false,
          link: null
        }
      }
    ]
  },
  {
    path: '/system',
    meta: {
      hide: false,
      keepAlive: true,
      title: '系统管理',
      icon: 'IconProSettingOutlined',
      noCache: false,
      link: null
    },
    redirect: 'user',
    children: [
      {
        path: '/system/user',
        component: '/system/user/index',
        meta: {
          hide: false,
          keepAlive: true,
          title: '用户管理',
          icon: 'IconProUserOutlined',
          noCache: false,
          link: null
        }
      },
      {
        path: '/system/role',
        component: '/system/role/index',
        meta: {
          hide: false,
          keepAlive: true,
          title: '角色管理',
          icon: 'IconProIdcardOutlined',
          noCache: false,
          link: null
        }
      },
      {
        path: '/system/menu',
        component: '/system/menu/index',
        meta: {
          hide: false,
          keepAlive: true,
          title: '菜单管理',
          icon: 'IconProAppstoreOutlined',
          noCache: false,
          link: null
        }
      },
      {
        path: '/system/dept',
        component: '/system/dept/index',
        meta: {
          hide: false,
          keepAlive: true,
          title: '部门管理',
          icon: 'IconProCityOutlined',
          noCache: false,
          link: null
        }
      },
      {
        path: '/system/post',
        component: '/system/post/index',
        meta: {
          hide: false,
          keepAlive: true,
          title: '岗位管理',
          icon: 'IconProSuitcaseOutlined',
          noCache: false,
          link: null
        }
      },
      {
        path: '/system/dict',
        component: '/system/dict/index',
        meta: {
          hide: false,
          keepAlive: true,
          title: '字典管理',
          icon: 'IconProBookOutlined',
          noCache: false,
          link: null
        }
      },
      {
        path: '/system/config',
        component: '/system/config/index',
        meta: {
          hide: false,
          keepAlive: true,
          title: '参数设置',
          icon: 'IconProControlOutlined',
          noCache: false,
          link: null
        }
      },
      {
        path: '/system/notice',
        component: '/system/notice/index',
        meta: {
          hide: false,
          keepAlive: true,
          title: '通知公告',
          icon: 'IconProMessageOutlined',
          noCache: false,
          link: null
        }
      },
      {
        path: '/system/log',
        component: '/ParentView',
        meta: {
          hide: false,
          keepAlive: true,
          title: '日志管理',
          icon: 'IconProLogOutlined',
          noCache: false,
          link: null
        },
        redirect: 'operlog',
        children: [
          {
            path: '/system/log/operlog',
            component: '/monitor/operlog/index',
            meta: {
              hide: false,
              keepAlive: true,
              title: '操作日志',
              icon: 'IconProFileOutlined',
              noCache: false,
              link: null
            }
          },
          {
            path: '/system/log/logininfor',
            component: '/monitor/logininfor/index',
            meta: {
              hide: false,
              keepAlive: true,
              title: '登录日志',
              icon: 'IconProCalendarOutlined',
              noCache: false,
              link: null
            }
          }
        ]
      }
    ]
  },
  {
    path: '/user',
    meta: {
      hide: false,
      keepAlive: true,
      title: '个人中心',
      icon: 'IconProSettingOutlined',
      noCache: false,
      link: null
    },
    redirect: 'user',
    children: [
      {
        path: '/user/profile',
        component: '/user/profile/index',
        meta: {
          hide: false,
          keepAlive: true,
          title: '我的资料',
          icon: 'IconProUserOutlined',
          noCache: false,
          link: null
        }
      },
      {
        path: '/user/message',
        component: '/user/message/index',
        meta: {
          hide: false,
          keepAlive: true,
          title: '我的消息',
          icon: 'IconProIdcardOutlined',
          noCache: false,
          link: null
        }
      }
    ]
  },
  {
    path: '/tool',
    meta: {
      hide: false,
      keepAlive: true,
      title: '系统工具',
      icon: 'IconProAppstoreAddOutlined',
      noCache: false,
      link: null
    },
    redirect: 'build',
    children: [
      {
        path: '/tool/build',
        component: '/tool/build/index',
        meta: {
          hide: false,
          keepAlive: true,
          title: '表单构建',
          icon: 'IconProFormOutlined',
          noCache: false,
          link: null
        }
      },
      {
        path: '/list/build',
        component: '/list/build',
        meta: {
          title: '列表构建',
          icon: 'IconProLinkOutlined',
          hide: false,
          props: {
            badge: {
              isDot: true
            }
          },
          lang: {
            zh_TW: '列表構建',
            en: 'List Build'
          }
        }
      },
      {
        path: '/tool/gen',
        component: '/tool/gen/index',
        meta: {
          hide: false,
          keepAlive: true,
          title: '代码生成',
          icon: 'IconProCodeOutlined',
          noCache: false,
          link: null
        }
      }
    ]
  }
  // 工作流
  // {
  //   name: 'Workflow11616',
  //   path: '/workflow',
  //   redirect: 'category',
  //   meta: {
  //     hide: false,
  //     keepAlive: true,
  //     title: '工作流',
  //     icon: 'IconProworkflow',
  //     noCache: false,
  //     link: null
  //   },
  //   children: [
  //     {
  //       path: 'category',
  //       component: '/workflow/category',
  //       meta: {
  //         hidden: false,
  //         title: '流程分类',
  //         icon: 'IconProcategory',
  //         noCache: false,
  //         keepAlive: true,
  //         link: null
  //       }
  //     },
  //     {
  //       path: 'processDefinition',
  //       component: '/workflow/processDefinition',
  //       meta: {
  //         hidden: false,
  //         title: '流程定义',
  //         icon: 'IconProprocess-definition',
  //         noCache: true,
  //         link: null
  //       }
  //     },
  //     {
  //       path: 'processInstance',
  //       component: '/workflow/processInstance',
  //       meta: {
  //         hidden: false,
  //         title: '流程实例',
  //         icon: 'IconProtree-table',
  //         noCache: true,
  //         link: null
  //       }
  //     },
  //     {
  //       path: 'allTaskWaiting',
  //       component: '/workflow/allTaskWaiting',
  //       meta: {
  //         hidden: false,
  //         title: '待办任务',
  //         icon: 'IconProwaiting',
  //         noCache: true,
  //         link: null
  //       }
  //     }
  //   ]
  // }
];
